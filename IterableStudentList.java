import java.util.*;

/**
 * Example 3: Advanced class that implements Iterable interface
 * This demonstrates how to make your own class iterable using Iterator pattern
 */
public class IterableStudentList implements Iterable<Student> {
    private ArrayList<Student> students;
    private String className;
    
    public IterableStudentList(String className) {
        this.className = className;
        this.students = new ArrayList<>();
    }
    
    // Add student method
    public void addStudent(Student student) {
        if (student != null) {
            students.add(student);
        }
    }
    
    // Remove student method
    public boolean removeStudent(String studentId) {
        Iterator<Student> iterator = students.iterator();
        while (iterator.hasNext()) {
            Student student = iterator.next();
            if (student.getStudentId().equals(studentId)) {
                iterator.remove();  // Safe removal during iteration
                return true;
            }
        }
        return false;
    }
    
    // Implement the iterator() method from Iterable interface
    @Override
    public Iterator<Student> iterator() {
        return new StudentIterator();
    }
    
    // Inner class that implements Iterator
    private class StudentIterator implements Iterator<Student> {
        private int currentIndex = 0;
        
        @Override
        public boolean hasNext() {
            return currentIndex < students.size();
        }
        
        @Override
        public Student next() {
            if (!hasNext()) {
                throw new NoSuchElementException("No more students");
            }
            return students.get(currentIndex++);
        }
        
        @Override
        public void remove() {
            if (currentIndex <= 0) {
                throw new IllegalStateException("Cannot remove before calling next()");
            }
            students.remove(--currentIndex);
        }
    }
    
    // Method to get iterator for honor roll students only
    public Iterator<Student> honorRollIterator() {
        return new HonorRollIterator();
    }
    
    // Another inner class for specialized iteration
    private class HonorRollIterator implements Iterator<Student> {
        private int currentIndex = 0;
        private Student nextHonorStudent = null;
        
        @Override
        public boolean hasNext() {
            findNextHonorStudent();
            return nextHonorStudent != null;
        }
        
        @Override
        public Student next() {
            if (!hasNext()) {
                throw new NoSuchElementException("No more honor roll students");
            }
            Student result = nextHonorStudent;
            currentIndex++;
            nextHonorStudent = null;
            return result;
        }
        
        private void findNextHonorStudent() {
            if (nextHonorStudent != null) {
                return;  // Already found
            }
            
            while (currentIndex < students.size()) {
                Student student = students.get(currentIndex);
                if (student.isOnHonorRoll()) {
                    nextHonorStudent = student;
                    return;
                }
                currentIndex++;
            }
        }
    }
    
    // Utility methods
    public int size() {
        return students.size();
    }
    
    public String getClassName() {
        return className;
    }
    
    public boolean isEmpty() {
        return students.isEmpty();
    }
}
