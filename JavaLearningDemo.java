import java.util.*;

/**
 * Main class to demonstrate all the concepts
 * Run this class to see examples of classes, private data, and iterators in action
 */
public class JavaLearningDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java Learning Demo: Classes, Private Data, and Iterators ===\n");
        
        // Example 1: Basic class usage with private data
        demonstrateBasicClass();
        
        // Example 2: Collection management
        demonstrateStudentList();
        
        // Example 3: Custom iterators
        demonstrateCustomIterators();
        
        // Example 4: Built-in Java collections and iterators
        demonstrateBuiltInIterators();
    }
    
    private static void demonstrateBasicClass() {
        System.out.println("1. BASIC CLASS WITH PRIVATE DATA");
        System.out.println("================================");
        
        // Create student objects
        Student student1 = new Student("Alice Johnson", 20, 3.8, "S001");
        Student student2 = new Student("<PERSON> Smith", 19, 3.2, "S002");
        
        // Display information
        student1.displayInfo();
        student2.displayInfo();
        
        // Try to modify data using setters (with validation)
        System.out.println("Trying to set invalid GPA (5.0) for Alice:");
        student1.setGpa(5.0);  // This should be rejected
        System.out.println("Alice's GPA is still: " + student1.getGpa());
        
        System.out.println("Setting valid GPA (3.9) for Alice:");
        student1.setGpa(3.9);
        System.out.println("Alice's new GPA: " + student1.getGpa());
        
        System.out.println();
    }
    
    private static void demonstrateStudentList() {
        System.out.println("2. STUDENT LIST WITH BASIC ITERATION");
        System.out.println("====================================");
        
        StudentList classList = new StudentList("Computer Science 101");
        
        // Add students
        classList.addStudent(new Student("Charlie Brown", 21, 3.6, "S003"));
        classList.addStudent(new Student("Diana Prince", 20, 3.9, "S004"));
        classList.addStudent(new Student("Edward Norton", 22, 2.8, "S005"));
        classList.addStudent(new Student("Fiona Green", 19, 3.7, "S006"));
        
        // Display all students
        classList.displayAllStudents();
        
        // Show honor roll
        classList.displayHonorRoll();
        
        // Show average GPA
        System.out.println("\nClass average GPA: " + 
                          String.format("%.2f", classList.calculateAverageGPA()));
        
        System.out.println();
    }
    
    private static void demonstrateCustomIterators() {
        System.out.println("3. CUSTOM ITERATORS");
        System.out.println("===================");
        
        IterableStudentList iterableList = new IterableStudentList("Advanced Java");
        
        // Add students
        iterableList.addStudent(new Student("Grace Hopper", 23, 4.0, "S007"));
        iterableList.addStudent(new Student("Alan Turing", 24, 3.8, "S008"));
        iterableList.addStudent(new Student("Ada Lovelace", 22, 3.9, "S009"));
        iterableList.addStudent(new Student("John Doe", 21, 3.1, "S010"));
        
        // Use the class as an Iterable (enhanced for loop works!)
        System.out.println("All students using enhanced for loop:");
        for (Student student : iterableList) {
            System.out.println("- " + student.getName() + " (GPA: " + student.getGpa() + ")");
        }
        
        // Use explicit iterator
        System.out.println("\nAll students using explicit iterator:");
        Iterator<Student> allStudents = iterableList.iterator();
        while (allStudents.hasNext()) {
            Student student = allStudents.next();
            System.out.println("- " + student.getName() + " (GPA: " + student.getGpa() + ")");
        }
        
        // Use custom honor roll iterator
        System.out.println("\nHonor roll students using custom iterator:");
        Iterator<Student> honorStudents = iterableList.honorRollIterator();
        while (honorStudents.hasNext()) {
            Student student = honorStudents.next();
            System.out.println("- " + student.getName() + " (GPA: " + student.getGpa() + ")");
        }
        
        System.out.println();
    }
    
    private static void demonstrateBuiltInIterators() {
        System.out.println("4. BUILT-IN JAVA COLLECTIONS AND ITERATORS");
        System.out.println("===========================================");
        
        // ArrayList example
        ArrayList<String> courses = new ArrayList<>();
        courses.add("Java Programming");
        courses.add("Data Structures");
        courses.add("Algorithms");
        courses.add("Database Systems");
        
        System.out.println("Courses using different iteration methods:");
        
        // Method 1: Traditional for loop
        System.out.println("\nTraditional for loop:");
        for (int i = 0; i < courses.size(); i++) {
            System.out.println((i + 1) + ". " + courses.get(i));
        }
        
        // Method 2: Enhanced for loop
        System.out.println("\nEnhanced for loop:");
        for (String course : courses) {
            System.out.println("- " + course);
        }
        
        // Method 3: Iterator
        System.out.println("\nUsing Iterator:");
        Iterator<String> courseIterator = courses.iterator();
        while (courseIterator.hasNext()) {
            System.out.println("* " + courseIterator.next());
        }
        
        // Method 4: ListIterator (can go backwards)
        System.out.println("\nUsing ListIterator (backwards):");
        ListIterator<String> listIterator = courses.listIterator(courses.size());
        while (listIterator.hasPrevious()) {
            System.out.println("< " + listIterator.previous());
        }
        
        System.out.println("\n=== Demo Complete! ===");
    }
}
