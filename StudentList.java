import java.util.*;

/**
 * Example 2: Class that manages a collection of Students
 * This demonstrates using ArrayList and basic iteration
 */
public class StudentList {
    // Private ArrayList to store students
    private ArrayList<Student> students;
    private String className;
    
    // Constructor
    public StudentList(String className) {
        this.className = className;
        this.students = new ArrayList<>();
    }
    
    // Method to add a student
    public void addStudent(Student student) {
        if (student != null) {
            students.add(student);
            System.out.println("Added student: " + student.getName());
        }
    }
    
    // Method to remove a student by student ID
    public boolean removeStudent(String studentId) {
        for (int i = 0; i < students.size(); i++) {
            if (students.get(i).getStudentId().equals(studentId)) {
                Student removed = students.remove(i);
                System.out.println("Removed student: " + removed.getName());
                return true;
            }
        }
        System.out.println("Student with ID " + studentId + " not found.");
        return false;
    }
    
    // Method to find a student by ID
    public Student findStudent(String studentId) {
        for (Student student : students) {  // Enhanced for loop
            if (student.getStudentId().equals(studentId)) {
                return student;
            }
        }
        return null;
    }
    
    // Method to display all students using traditional for loop
    public void displayAllStudents() {
        System.out.println("\n=== " + className + " Student List ===");
        if (students.isEmpty()) {
            System.out.println("No students in the class.");
            return;
        }
        
        for (int i = 0; i < students.size(); i++) {
            System.out.println((i + 1) + ". " + students.get(i));
        }
    }
    
    // Method to display honor roll students using enhanced for loop
    public void displayHonorRoll() {
        System.out.println("\n=== Honor Roll Students ===");
        boolean hasHonorStudents = false;
        
        for (Student student : students) {
            if (student.isOnHonorRoll()) {
                System.out.println(student.getName() + " - GPA: " + student.getGpa());
                hasHonorStudents = true;
            }
        }
        
        if (!hasHonorStudents) {
            System.out.println("No students on honor roll.");
        }
    }
    
    // Method to calculate average GPA
    public double calculateAverageGPA() {
        if (students.isEmpty()) {
            return 0.0;
        }
        
        double total = 0.0;
        for (Student student : students) {
            total += student.getGpa();
        }
        
        return total / students.size();
    }
    
    // Getter for number of students
    public int getStudentCount() {
        return students.size();
    }
    
    // Getter for class name
    public String getClassName() {
        return className;
    }
}
