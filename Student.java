/**
 * Example 1: Basic Class with Private Data
 * This demonstrates encapsulation - keeping data private and providing controlled access
 */
public class Student {
    // Private data members - cannot be accessed directly from outside the class
    private String name;
    private int age;
    private double gpa;
    private String studentId;
    
    // Constructor - used to create and initialize objects
    public Student(String name, int age, double gpa, String studentId) {
        this.name = name;
        this.age = age;
        this.gpa = gpa;
        this.studentId = studentId;
    }
    
    // Getter methods (accessors) - provide controlled read access to private data
    public String getName() {
        return name;
    }
    
    public int getAge() {
        return age;
    }
    
    public double getGpa() {
        return gpa;
    }
    
    public String getStudentId() {
        return studentId;
    }
    
    // Setter methods (mutators) - provide controlled write access to private data
    public void setName(String name) {
        if (name != null && !name.trim().isEmpty()) {
            this.name = name;
        }
    }
    
    public void setAge(int age) {
        if (age >= 0 && age <= 150) {  // Basic validation
            this.age = age;
        }
    }
    
    public void setGpa(double gpa) {
        if (gpa >= 0.0 && gpa <= 4.0) {  // GPA validation
            this.gpa = gpa;
        }
    }
    
    // Method to display student information
    public void displayInfo() {
        System.out.println("Student Information:");
        System.out.println("Name: " + name);
        System.out.println("Age: " + age);
        System.out.println("GPA: " + gpa);
        System.out.println("Student ID: " + studentId);
        System.out.println("------------------------");
    }
    
    // Method to check if student is on honor roll
    public boolean isOnHonorRoll() {
        return gpa >= 3.5;
    }
    
    // Override toString method for easy printing
    @Override
    public String toString() {
        return "Student{name='" + name + "', age=" + age + 
               ", gpa=" + gpa + ", studentId='" + studentId + "'}";
    }
}
